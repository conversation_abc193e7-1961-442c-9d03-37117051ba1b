<template>
  <div 
    ref="containerRef" 
    class="simple-virtual-list" 
    :style="{ height: `${height}px`, overflow: 'auto' }"
    @scroll="handleScroll"
  >
    <div :style="{ height: `${totalHeight}px`, position: 'relative' }">
      <div
        v-for="item in visibleItems"
        :key="item.key"
        :style="{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: `${itemHeight}px`,
          transform: `translateY(${item.offset}px)`
        }"
      >
        <slot :item="item.data" :index="item.index" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  items: T[]
  height: number
  itemHeight?: number
  overscan?: number
  getItemKey?: (item: T, index: number) => string | number
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 60,
  overscan: 3,
  getItemKey: (item: T, index: number) => index
})

const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)

// 计算总高度
const totalHeight = computed(() => props.items.length * props.itemHeight)

// 计算可见区域
const visibleRange = computed(() => {
  const containerHeight = props.height
  const startIndex = Math.floor(scrollTop.value / props.itemHeight)
  const endIndex = Math.min(
    props.items.length - 1,
    Math.ceil((scrollTop.value + containerHeight) / props.itemHeight)
  )
  
  // 添加 overscan
  const overscanStart = Math.max(0, startIndex - props.overscan)
  const overscanEnd = Math.min(props.items.length - 1, endIndex + props.overscan)
  
  return {
    start: overscanStart,
    end: overscanEnd
  }
})

// 计算可见项目
const visibleItems = computed(() => {
  const { start, end } = visibleRange.value
  const items = []
  
  for (let i = start; i <= end; i++) {
    if (i < props.items.length) {
      items.push({
        key: props.getItemKey(props.items[i], i),
        index: i,
        offset: i * props.itemHeight,
        data: props.items[i]
      })
    }
  }
  
  return items
})

// 处理滚动事件
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
}

// 滚动到指定项目
const scrollToItem = (index: number, align: 'start' | 'center' | 'end' = 'start') => {
  if (!containerRef.value) return
  
  const itemOffset = index * props.itemHeight
  let scrollOffset = itemOffset
  
  if (align === 'center') {
    scrollOffset = itemOffset - (props.height - props.itemHeight) / 2
  } else if (align === 'end') {
    scrollOffset = itemOffset - props.height + props.itemHeight
  }
  
  scrollOffset = Math.max(0, Math.min(scrollOffset, totalHeight.value - props.height))
  containerRef.value.scrollTop = scrollOffset
}

// 滚动到顶部
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = totalHeight.value
  }
}

// 暴露方法
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom
})

// 调试信息
onMounted(() => {
  console.log('SimpleVirtualList mounted with', props.items.length, 'items')
})
</script>

<style scoped>
.simple-virtual-list {
  /* 确保滚动条样式一致 */
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.simple-virtual-list::-webkit-scrollbar {
  width: 6px;
}

.simple-virtual-list::-webkit-scrollbar-track {
  background: transparent;
}

.simple-virtual-list::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.simple-virtual-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}
</style>
