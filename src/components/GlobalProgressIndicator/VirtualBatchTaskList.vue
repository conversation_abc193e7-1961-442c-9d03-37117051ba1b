<template>
  <div ref="containerRef" class="virtual-batch-task-list" :style="{ height: `${height}px`, overflow: 'auto' }">
    <div :style="{ height: `${virtualizer.getTotalSize()}px`, position: 'relative' }">
      <div
        v-for="virtualItem in virtualizer.getVirtualItems()"
        :key="virtualItem.key"
        :style="{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: `${virtualItem.size}px`,
          transform: `translateY(${virtualItem.start}px)`
        }"
      >
        <div class="virtual-item-content">
          <slot
            :item="flattenedItems[virtualItem.index]"
            :index="virtualItem.index"
            :virtualItem="virtualItem"
            :isExpanded="isExpanded"
            :toggleExpanded="toggleExpanded"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'

interface BatchTaskItem {
  id: string
  type: 'batch' | 'subtask'
  batchId?: string
  data: T
}

interface Props {
  batchTasks: T[]
  height: number
  estimateSize?: number
  overscan?: number
  getItemKey?: (item: BatchTaskItem, index: number) => string | number
  getSubTasks?: (batchTask: T) => any[]
  defaultExpanded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  estimateSize: 60,
  overscan: 5,
  getItemKey: (item: BatchTaskItem, index: number) => `${item.type}-${item.id}-${index}`,
  getSubTasks: () => [],
  defaultExpanded: false
})

const containerRef = ref<HTMLElement>()

// 展开状态管理
const expandedBatches = ref<Set<string>>(new Set())

// 初始化展开状态
onMounted(() => {
  if (props.defaultExpanded) {
    props.batchTasks.forEach(task => {
      if (task && typeof task === 'object' && 'id' in task) {
        expandedBatches.value.add((task as any).id)
      }
    })
  }
})

// 切换展开状态
const toggleExpanded = (batchId: string) => {
  if (expandedBatches.value.has(batchId)) {
    expandedBatches.value.delete(batchId)
  } else {
    expandedBatches.value.add(batchId)
  }
}

// 检查是否展开
const isExpanded = (batchId: string) => {
  return expandedBatches.value.has(batchId)
}

// 将批量任务和子任务扁平化为单一列表
const flattenedItems = computed<BatchTaskItem[]>(() => {
  const result: BatchTaskItem[] = []
  
  props.batchTasks.forEach(batchTask => {
    if (!batchTask || typeof batchTask !== 'object' || !('id' in batchTask)) {
      return
    }
    
    const batchId = (batchTask as any).id
    
    // 添加批量任务本身
    result.push({
      id: batchId,
      type: 'batch',
      data: batchTask
    })
    
    // 如果展开，添加子任务
    if (expandedBatches.value.has(batchId)) {
      const subTasks = props.getSubTasks(batchTask)
      subTasks.forEach((subTask, index) => {
        result.push({
          id: `${batchId}-sub-${index}`,
          type: 'subtask',
          batchId: batchId,
          data: subTask
        })
      })
    }
  })
  
  return result
})

// 动态计算项目高度
const getItemSize = (index: number) => {
  const item = flattenedItems.value[index]
  if (!item) return props.estimateSize
  
  // 批量任务通常比子任务高一些
  if (item.type === 'batch') {
    return props.estimateSize + 10 // 批量任务稍高
  } else {
    return props.estimateSize - 5 // 子任务稍低
  }
}

// 创建虚拟化器
const virtualizer = useVirtualizer(
  computed(() => ({
    count: flattenedItems.value.length,
    getScrollElement: () => containerRef.value,
    estimateSize: (index: number) => getItemSize(index),
    overscan: props.overscan,
    getItemKey: (index: number) => props.getItemKey(flattenedItems.value[index], index)
  }))
)

// 监听扁平化项目变化，重新计算虚拟化
watch(() => flattenedItems.value.length, () => {
  virtualizer.measure()
}, { flush: 'post' })

// 监听展开状态变化，重新计算虚拟化
watch(expandedBatches, () => {
  virtualizer.measure()
}, { deep: true, flush: 'post' })

// 提供滚动到指定项的方法
const scrollToItem = (index: number, options?: { align?: 'start' | 'center' | 'end' | 'auto' }) => {
  virtualizer.scrollToIndex(index, options)
}

// 提供滚动到顶部的方法
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 提供滚动到底部的方法
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = containerRef.value.scrollHeight
  }
}

// 展开所有批量任务
const expandAll = () => {
  props.batchTasks.forEach(task => {
    if (task && typeof task === 'object' && 'id' in task) {
      expandedBatches.value.add((task as any).id)
    }
  })
}

// 折叠所有批量任务
const collapseAll = () => {
  expandedBatches.value.clear()
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  expandAll,
  collapseAll,
  toggleExpanded,
  isExpanded,
  virtualizer
})

// 性能优化：在组件卸载时清理
onUnmounted(() => {
  expandedBatches.value.clear()
})
</script>

<style scoped>
.virtual-batch-task-list {
  /* 确保滚动条样式一致 */
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.virtual-batch-task-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-batch-task-list::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-batch-task-list::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.virtual-batch-task-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

.virtual-item-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
