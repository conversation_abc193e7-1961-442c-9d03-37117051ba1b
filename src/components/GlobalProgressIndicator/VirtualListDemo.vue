<template>
  <div class="p-4 space-y-4">
    <div class="flex gap-4 items-center">
      <h2 class="text-lg font-semibold">虚拟列表性能测试</h2>
      <Button @click="generateTestData" variant="outline" size="sm">
        生成测试数据
      </Button>
      <Button @click="clearTestData" variant="outline" size="sm">
        清除数据
      </Button>
      <Button @click="toggleMonitoring" variant="outline" size="sm">
        {{ isMonitoring ? '停止' : '开始' }}监控
      </Button>
    </div>

    <div class="grid grid-cols-3 gap-4 text-sm">
      <div class="p-2 bg-gray-100 rounded">
        <div class="font-medium">当前任务</div>
        <div>{{ testProgressTasks.length }} 项</div>
      </div>
      <div class="p-2 bg-gray-100 rounded">
        <div class="font-medium">上传历史</div>
        <div>{{ testUploadHistory.length }} 项</div>
      </div>
      <div class="p-2 bg-gray-100 rounded">
        <div class="font-medium">下载历史</div>
        <div>{{ testDownloadHistory.length }} 项</div>
      </div>
    </div>

    <!-- 模拟进度面板 -->
    <div class="p-4 w-96 max-w-sm rounded-lg border bg-background">
      <div class="flex justify-between items-center mb-3">
        <span class="text-sm font-medium">测试任务管理器</span>
        <div class="flex gap-1">
          <Button v-for="tab in tabs" :key="tab.key" variant="ghost" size="sm"
            :class="{ 'bg-secondary': activeTab === tab.key }" @click="activeTab = tab.key">
            {{ tab.label }} ({{ tab.count }})
          </Button>
        </div>
      </div>

      <!-- 虚拟列表测试 -->
      <div class="max-h-64">
        <template v-if="activeTab === 'current'">
          <VirtualTaskList v-if="allCurrentTasks.length > 0" :items="allCurrentTasks" :height="256" :estimate-size="65"
            :overscan="3" :get-item-key="(item, index) => `${item.type}-${item.id}`" ref="currentTasksVirtualList">
            <template #default="{ item }">
              <div class="p-2 border-b">
                <div class="flex justify-between items-center text-xs">
                  <div class="flex flex-1 gap-2 items-center min-w-0">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="font-medium truncate">{{ item.data.fileName || item.data.batchName }}</span>
                    <span class="text-muted-foreground">{{ item.type }}</span>
                  </div>
                  <span class="text-muted-foreground">
                    {{ item.data.progress || 0 }}%
                  </span>
                </div>
                <div class="mt-1 w-full h-1 rounded-full bg-secondary">
                  <div class="h-1 rounded-full transition-all duration-300 bg-primary"
                    :style="{ width: `${item.data.progress || 0}%` }" />
                </div>
              </div>
            </template>
          </VirtualTaskList>
          <div v-else class="py-8 text-center text-muted-foreground">
            <div class="text-sm">暂无任务</div>
          </div>
        </template>

        <template v-else>
          <VirtualTaskList v-if="currentHistoryTasks.length > 0" :items="currentHistoryTasks" :height="256"
            :estimate-size="70" :overscan="3" :get-item-key="(item, index) => `history-${item.id}`"
            ref="historyTasksVirtualList">
            <template #default="{ item }">
              <div class="p-2 border-b">
                <div class="flex justify-between items-center text-xs">
                  <div class="flex flex-1 gap-2 items-center min-w-0">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="font-medium truncate">{{ item.fileName || item.batchName }}</span>
                    <span class="text-muted-foreground">{{ item.status }}</span>
                  </div>
                  <span class="text-muted-foreground">
                    {{ formatDate(item.endTime) }}
                  </span>
                </div>
              </div>
            </template>
          </VirtualTaskList>
          <div v-else class="py-8 text-center text-muted-foreground">
            <div class="text-sm">暂无历史记录</div>
          </div>
        </template>
      </div>
    </div>

    <!-- 性能信息 -->
    <div v-if="performanceInfo" class="p-4 text-sm bg-gray-50 rounded">
      <div class="mb-2 font-medium">性能信息</div>
      <div class="grid grid-cols-2 gap-2">
        <div>DOM节点: {{ performanceInfo.domNodeCount }}</div>
        <div>监控状态: {{ performanceInfo.isMonitoring ? '运行中' : '已停止' }}</div>
        <div v-if="performanceInfo.memoryInfo">
          内存使用: {{ performanceInfo.memoryInfo.used }}MB / {{ performanceInfo.memoryInfo.total }}MB
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Button } from '@/components/ui/button'
import VirtualTaskList from './VirtualTaskList.vue'
import { generateMixedTestData } from './utils/testDataGenerator'
import { performanceMonitor } from './utils/performanceMonitor'
import type { ProgressTask, HistoryTask, BatchHistoryTask } from '@/composables/useGlobalProgress'

// 测试数据
const testProgressTasks = ref<ProgressTask[]>([])
const testUploadHistory = ref<HistoryTask[]>([])
const testDownloadHistory = ref<HistoryTask[]>([])
const testBatchHistory = ref<BatchHistoryTask[]>([])

// UI 状态
const activeTab = ref<'current' | 'upload' | 'download'>('current')
const isMonitoring = ref(false)
const performanceInfo = ref<any>(null)

// 虚拟列表引用
const currentTasksVirtualList = ref()
const historyTasksVirtualList = ref()

// 标签页配置
const tabs = computed(() => [
  {
    key: 'current' as const,
    label: '当前',
    count: allCurrentTasks.value.length
  },
  {
    key: 'upload' as const,
    label: '上传',
    count: testUploadHistory.value.length
  },
  {
    key: 'download' as const,
    label: '下载',
    count: testDownloadHistory.value.length
  }
])

// 为虚拟列表准备的当前任务数据
const allCurrentTasks = computed(() => {
  const result: any[] = []

  // 添加普通任务
  testProgressTasks.value.forEach(task => {
    result.push({
      id: task.id,
      type: 'task',
      data: task
    })
  })

  return result
})

// 当前显示的历史任务
const currentHistoryTasks = computed(() => {
  if (activeTab.value === 'upload') {
    return [...testUploadHistory.value, ...testBatchHistory.value.filter(t => t.batchType === 'upload')]
  } else if (activeTab.value === 'download') {
    return [...testDownloadHistory.value, ...testBatchHistory.value.filter(t => t.batchType === 'download')]
  }
  return []
})

// 生成测试数据
const generateTestData = () => {
  console.log('🧪 生成测试数据...')
  const testData = generateMixedTestData(10000, 50000, 20000)

  testProgressTasks.value = testData.progressTasks
  testUploadHistory.value = testData.historyTasks.filter(t => t.type === 'upload')
  testDownloadHistory.value = testData.historyTasks.filter(t => t.type === 'download')
  testBatchHistory.value = testData.batchHistoryTasks

  console.log('✅ 测试数据生成完成:', {
    progressTasks: testProgressTasks.value.length,
    uploadHistory: testUploadHistory.value.length,
    downloadHistory: testDownloadHistory.value.length,
    batchHistory: testBatchHistory.value.length
  })

  updatePerformanceInfo()
}

// 清除测试数据
const clearTestData = () => {
  testProgressTasks.value = []
  testUploadHistory.value = []
  testDownloadHistory.value = []
  testBatchHistory.value = []

  console.log('🧹 测试数据已清除')
  updatePerformanceInfo()
}

// 切换性能监控
const toggleMonitoring = () => {
  if (isMonitoring.value) {
    performanceMonitor.stopMonitoring()
    isMonitoring.value = false
  } else {
    performanceMonitor.startMonitoring()
    isMonitoring.value = true
  }

  updatePerformanceInfo()
}

// 更新性能信息
const updatePerformanceInfo = () => {
  performanceInfo.value = performanceMonitor.getPerformanceReport()
}

// 格式化日期
const formatDate = (date: Date) => {
  return new Date(date).toLocaleTimeString()
}

// 定期更新性能信息
let performanceInterval: NodeJS.Timeout

onMounted(() => {
  performanceInterval = setInterval(updatePerformanceInfo, 2000)
  updatePerformanceInfo()
})

onUnmounted(() => {
  if (performanceInterval) {
    clearInterval(performanceInterval)
  }
  if (isMonitoring.value) {
    performanceMonitor.stopMonitoring()
  }
})
</script>
