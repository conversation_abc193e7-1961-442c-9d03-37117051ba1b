/**
 * 性能监控工具
 * 用于监控虚拟列表的性能表现
 */

interface PerformanceMetrics {
  renderTime: number
  scrollTime: number
  memoryUsage: number
  domNodeCount: number
  timestamp: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private observers: PerformanceObserver[] = []
  private isMonitoring = false

  /**
   * 开始性能监控
   */
  startMonitoring() {
    if (this.isMonitoring) return

    this.isMonitoring = true
    console.log('🔍 开始性能监控')

    // 监控渲染性能
    this.observeRenderPerformance()
    
    // 监控内存使用
    this.observeMemoryUsage()
    
    // 监控DOM节点数量
    this.observeDOMNodes()
  }

  /**
   * 停止性能监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    console.log('⏹️ 停止性能监控')

    // 清理所有观察器
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }

  /**
   * 监控渲染性能
   */
  private observeRenderPerformance() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.entryType === 'measure' && entry.name.includes('virtual-list')) {
          console.log(`📊 虚拟列表渲染时间: ${entry.duration.toFixed(2)}ms`)
        }
      })
    })

    observer.observe({ entryTypes: ['measure'] })
    this.observers.push(observer)
  }

  /**
   * 监控内存使用
   */
  private observeMemoryUsage() {
    const checkMemory = () => {
      if (!this.isMonitoring) return

      // @ts-ignore - performance.memory 在某些浏览器中可用
      const memory = (performance as any).memory
      if (memory) {
        const memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
        }
        
        console.log(`💾 内存使用: ${memoryUsage.used}MB / ${memoryUsage.total}MB (限制: ${memoryUsage.limit}MB)`)
      }

      setTimeout(checkMemory, 5000) // 每5秒检查一次
    }

    checkMemory()
  }

  /**
   * 监控DOM节点数量
   */
  private observeDOMNodes() {
    const checkDOMNodes = () => {
      if (!this.isMonitoring) return

      const nodeCount = document.querySelectorAll('*').length
      console.log(`🌳 DOM节点数量: ${nodeCount}`)

      setTimeout(checkDOMNodes, 3000) // 每3秒检查一次
    }

    checkDOMNodes()
  }

  /**
   * 测量虚拟列表滚动性能
   */
  measureScrollPerformance(container: HTMLElement) {
    if (!container) return

    let scrollStartTime = 0
    let frameCount = 0
    let lastFrameTime = 0

    const onScrollStart = () => {
      scrollStartTime = performance.now()
      frameCount = 0
      lastFrameTime = scrollStartTime
    }

    const onScrollEnd = () => {
      const scrollEndTime = performance.now()
      const totalTime = scrollEndTime - scrollStartTime
      const fps = frameCount > 0 ? Math.round(1000 / (totalTime / frameCount)) : 0
      
      console.log(`📜 滚动性能: 总时间 ${totalTime.toFixed(2)}ms, 平均FPS: ${fps}`)
    }

    const onScroll = () => {
      const currentTime = performance.now()
      frameCount++
      
      // 防抖处理，滚动停止后触发结束事件
      clearTimeout((onScroll as any).timeout)
      ;(onScroll as any).timeout = setTimeout(onScrollEnd, 150)
      
      if (frameCount === 1) {
        onScrollStart()
      }
    }

    container.addEventListener('scroll', onScroll, { passive: true })

    // 返回清理函数
    return () => {
      container.removeEventListener('scroll', onScroll)
      clearTimeout((onScroll as any).timeout)
    }
  }

  /**
   * 测量组件渲染时间
   */
  measureRenderTime(componentName: string, renderFn: () => void) {
    const startTime = performance.now()
    
    renderFn()
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    console.log(`⚡ ${componentName} 渲染时间: ${renderTime.toFixed(2)}ms`)
    
    // 记录性能指标
    performance.mark(`${componentName}-render-start`)
    performance.mark(`${componentName}-render-end`)
    performance.measure(`${componentName}-render`, `${componentName}-render-start`, `${componentName}-render-end`)
    
    return renderTime
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const report = {
      isMonitoring: this.isMonitoring,
      metricsCount: this.metrics.length,
      memoryInfo: this.getMemoryInfo(),
      domNodeCount: document.querySelectorAll('*').length,
      timestamp: Date.now()
    }

    console.log('📈 性能报告:', report)
    return report
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo() {
    // @ts-ignore
    const memory = (performance as any).memory
    if (!memory) return null

    return {
      used: Math.round(memory.usedJSHeapSize / 1048576),
      total: Math.round(memory.totalJSHeapSize / 1048576),
      limit: Math.round(memory.jsHeapSizeLimit / 1048576)
    }
  }

  /**
   * 清理所有数据
   */
  cleanup() {
    this.stopMonitoring()
    this.metrics = []
    console.log('🧹 性能监控数据已清理')
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()

/**
 * 性能测试装饰器
 * 用于测试函数执行时间
 */
export function measurePerformance(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value

  descriptor.value = function (...args: any[]) {
    const startTime = performance.now()
    const result = method.apply(this, args)
    const endTime = performance.now()
    
    console.log(`⏱️ ${propertyName} 执行时间: ${(endTime - startTime).toFixed(2)}ms`)
    
    return result
  }

  return descriptor
}

/**
 * 虚拟列表性能测试工具
 */
export class VirtualListPerformanceTester {
  private container: HTMLElement | null = null
  private scrollCleanup: (() => void) | null = null

  /**
   * 初始化性能测试
   */
  init(container: HTMLElement) {
    this.container = container
    this.scrollCleanup = performanceMonitor.measureScrollPerformance(container)
    performanceMonitor.startMonitoring()
    
    console.log('🧪 虚拟列表性能测试已初始化')
  }

  /**
   * 测试大量数据渲染性能
   */
  testLargeDataRendering(itemCount: number) {
    console.log(`🔬 开始测试 ${itemCount} 项数据的渲染性能`)
    
    const startTime = performance.now()
    
    // 这里应该触发虚拟列表重新渲染
    // 具体实现取决于虚拟列表组件的API
    
    const endTime = performance.now()
    console.log(`📊 ${itemCount} 项数据渲染完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)
  }

  /**
   * 测试滚动性能
   */
  testScrollPerformance() {
    if (!this.container) return

    console.log('🔄 开始滚动性能测试')
    
    // 模拟快速滚动
    const scrollHeight = this.container.scrollHeight
    const clientHeight = this.container.clientHeight
    const maxScroll = scrollHeight - clientHeight
    
    let currentScroll = 0
    const scrollStep = maxScroll / 20 // 分20步滚动
    
    const scrollInterval = setInterval(() => {
      currentScroll += scrollStep
      this.container!.scrollTop = currentScroll
      
      if (currentScroll >= maxScroll) {
        clearInterval(scrollInterval)
        console.log('✅ 滚动性能测试完成')
      }
    }, 50) // 每50ms滚动一次
  }

  /**
   * 清理测试环境
   */
  cleanup() {
    if (this.scrollCleanup) {
      this.scrollCleanup()
      this.scrollCleanup = null
    }
    
    performanceMonitor.stopMonitoring()
    this.container = null
    
    console.log('🧹 虚拟列表性能测试已清理')
  }
}

// 导出便捷函数
export function startPerformanceMonitoring() {
  performanceMonitor.startMonitoring()
}

export function stopPerformanceMonitoring() {
  performanceMonitor.stopMonitoring()
}

export function getPerformanceReport() {
  return performanceMonitor.getPerformanceReport()
}
