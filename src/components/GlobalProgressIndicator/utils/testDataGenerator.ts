import type { ProgressTask, HistoryTask, BatchHistoryTask } from '@/composables/useGlobalProgress'

/**
 * 生成测试用的进度任务数据
 * @param count 生成数量
 * @returns 进度任务数组
 */
export function generateTestProgressTasks(count: number): ProgressTask[] {
  const tasks: ProgressTask[] = []
  
  for (let i = 0; i < count; i++) {
    const isUpload = Math.random() > 0.5
    const status = ['pending', 'in-progress', 'paused', 'error'][Math.floor(Math.random() * 4)] as ProgressTask['status']
    
    tasks.push({
      id: `test-task-${i}`,
      type: isUpload ? 'upload' : 'download',
      fileName: `测试文件_${i}.${getRandomExtension()}`,
      size: formatFileSize(Math.floor(Math.random() * 1000000000)), // 随机文件大小
      progress: status === 'error' ? 0 : Math.floor(Math.random() * 100),
      status,
      startTime: new Date(Date.now() - Math.random() * 3600000), // 随机开始时间
      endTime: status === 'completed' ? new Date() : undefined,
      error: status === 'error' ? '测试错误信息' : undefined,
      bytesUploaded: Math.floor(Math.random() * 50000000),
      totalBytes: Math.floor(Math.random() * 100000000)
    })
  }
  
  return tasks
}

/**
 * 生成测试用的历史任务数据
 * @param count 生成数量
 * @returns 历史任务数组
 */
export function generateTestHistoryTasks(count: number): HistoryTask[] {
  const tasks: HistoryTask[] = []
  
  for (let i = 0; i < count; i++) {
    const isUpload = Math.random() > 0.5
    const status = ['completed', 'error', 'cancelled'][Math.floor(Math.random() * 3)] as HistoryTask['status']
    const startTime = new Date(Date.now() - Math.random() * 86400000) // 随机开始时间（24小时内）
    const endTime = new Date(startTime.getTime() + Math.random() * 3600000) // 随机结束时间
    
    tasks.push({
      id: `test-history-${i}`,
      type: isUpload ? 'upload' : 'download',
      fileName: `历史文件_${i}.${getRandomExtension()}`,
      size: formatFileSize(Math.floor(Math.random() * 1000000000)),
      startTime,
      endTime,
      duration: endTime.getTime() - startTime.getTime(),
      status,
      error: status === 'error' ? '历史任务错误信息' : undefined
    })
  }
  
  return tasks
}

/**
 * 生成测试用的批量历史任务数据
 * @param count 生成数量
 * @returns 批量历史任务数组
 */
export function generateTestBatchHistoryTasks(count: number): BatchHistoryTask[] {
  const tasks: BatchHistoryTask[] = []
  
  for (let i = 0; i < count; i++) {
    const isUpload = Math.random() > 0.5
    const status = ['completed', 'error', 'cancelled'][Math.floor(Math.random() * 3)] as BatchHistoryTask['status']
    const startTime = new Date(Date.now() - Math.random() * 86400000)
    const endTime = new Date(startTime.getTime() + Math.random() * 7200000) // 批量任务通常耗时更长
    const totalFiles = Math.floor(Math.random() * 100) + 10 // 10-110个文件
    const completedFiles = Math.floor(totalFiles * (0.7 + Math.random() * 0.3)) // 70%-100%完成
    const failedFiles = totalFiles - completedFiles
    
    // 生成子任务
    const subTasks = []
    for (let j = 0; j < totalFiles; j++) {
      subTasks.push({
        id: `sub-task-${i}-${j}`,
        fileName: `批量文件_${i}_${j}.${getRandomExtension()}`,
        fileSize: Math.floor(Math.random() * 100000000),
        status: j < completedFiles ? 'completed' as const : 'error' as const,
        error: j >= completedFiles ? '子任务错误' : undefined
      })
    }
    
    tasks.push({
      id: `test-batch-${i}`,
      type: 'batch',
      batchType: isUpload ? 'upload' : 'download',
      batchName: `批量任务_${i}`,
      folderPath: `/测试文件夹_${i}`,
      totalFiles,
      completedFiles,
      failedFiles,
      totalSize: Math.floor(Math.random() * 10000000000), // 随机总大小
      status,
      startTime,
      endTime,
      duration: endTime.getTime() - startTime.getTime(),
      error: status === 'error' ? '批量任务错误信息' : undefined,
      subTasks
    })
  }
  
  return tasks
}

/**
 * 随机文件扩展名
 */
function getRandomExtension(): string {
  const extensions = ['jpg', 'png', 'pdf', 'docx', 'xlsx', 'mp4', 'mp3', 'zip', 'txt', 'pptx']
  return extensions[Math.floor(Math.random() * extensions.length)]
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 生成混合测试数据（包含所有类型的任务）
 * @param progressCount 进度任务数量
 * @param historyCount 历史任务数量
 * @param batchHistoryCount 批量历史任务数量
 * @returns 包含所有测试数据的对象
 */
export function generateMixedTestData(
  progressCount: number = 50,
  historyCount: number = 500,
  batchHistoryCount: number = 20
) {
  return {
    progressTasks: generateTestProgressTasks(progressCount),
    historyTasks: generateTestHistoryTasks(historyCount),
    batchHistoryTasks: generateTestBatchHistoryTasks(batchHistoryCount)
  }
}

/**
 * 在开发环境中启用测试数据
 * 这个函数可以在开发时调用来测试虚拟列表的性能
 */
export function enableTestDataInDev() {
  if (import.meta.env.DEV) {
    console.log('🧪 启用测试数据模式')
    const testData = generateMixedTestData(100, 1000, 50)
    console.log('📊 生成的测试数据:', {
      progressTasks: testData.progressTasks.length,
      historyTasks: testData.historyTasks.length,
      batchHistoryTasks: testData.batchHistoryTasks.length
    })
    return testData
  }
  return null
}
