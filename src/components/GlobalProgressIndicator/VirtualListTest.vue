<template>
  <div class="p-4 space-y-4">
    <div class="flex gap-4 items-center">
      <h2 class="text-lg font-semibold">虚拟列表修复测试</h2>
      <Button @click="generateTestData" variant="outline" size="sm">
        生成测试数据 ({{ testItems.length }} 项)
      </Button>
      <Button @click="clearTestData" variant="outline" size="sm">
        清除数据
      </Button>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <!-- TanStack Virtual 测试 -->
      <div class="border rounded-lg p-4">
        <h3 class="font-medium mb-2">TanStack Virtual 测试</h3>
        <div class="text-sm text-gray-600 mb-2">
          使用 @tanstack/vue-virtual
        </div>
        <VirtualTaskList
          :items="testItems"
          :height="300"
          :estimate-size="60"
          :overscan="3"
          :get-item-key="(item, index) => `tanstack-${item.id}`"
        >
          <template #default="{ item }">
            <div class="p-2 border-b bg-blue-50">
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">{{ item.name }}</span>
                <span class="text-gray-500">#{{ item.id }}</span>
              </div>
              <div class="text-xs text-gray-600">TanStack Virtual</div>
            </div>
          </template>
        </VirtualTaskList>
      </div>

      <!-- Simple Virtual 测试 -->
      <div class="border rounded-lg p-4">
        <h3 class="font-medium mb-2">Simple Virtual 测试</h3>
        <div class="text-sm text-gray-600 mb-2">
          使用自定义简单实现
        </div>
        <SimpleVirtualList
          :items="testItems"
          :height="300"
          :item-height="60"
          :overscan="3"
          :get-item-key="(item, index) => `simple-${item.id}`"
        >
          <template #default="{ item }">
            <div class="p-2 border-b bg-green-50">
              <div class="flex items-center justify-between text-sm">
                <span class="font-medium">{{ item.name }}</span>
                <span class="text-gray-500">#{{ item.id }}</span>
              </div>
              <div class="text-xs text-gray-600">Simple Virtual</div>
            </div>
          </template>
        </SimpleVirtualList>
      </div>
    </div>

    <!-- 错误日志 -->
    <div v-if="errorLogs.length > 0" class="border rounded-lg p-4 bg-red-50">
      <h3 class="font-medium mb-2 text-red-700">错误日志</h3>
      <div class="space-y-1">
        <div v-for="(error, index) in errorLogs" :key="index" class="text-sm text-red-600">
          {{ error }}
        </div>
      </div>
      <Button @click="clearErrors" variant="outline" size="sm" class="mt-2">
        清除错误日志
      </Button>
    </div>

    <!-- 性能信息 -->
    <div class="border rounded-lg p-4 bg-gray-50">
      <h3 class="font-medium mb-2">性能信息</h3>
      <div class="grid grid-cols-3 gap-4 text-sm">
        <div>
          <div class="font-medium">测试数据量</div>
          <div>{{ testItems.length }} 项</div>
        </div>
        <div>
          <div class="font-medium">DOM 节点数</div>
          <div>{{ domNodeCount }}</div>
        </div>
        <div>
          <div class="font-medium">内存使用</div>
          <div>{{ memoryUsage }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Button } from '@/components/ui/button'
import VirtualTaskList from './VirtualTaskList.vue'
import SimpleVirtualList from './SimpleVirtualList.vue'

interface TestItem {
  id: number
  name: string
  type: string
}

const testItems = ref<TestItem[]>([])
const errorLogs = ref<string[]>([])
const domNodeCount = ref(0)
const memoryUsage = ref('N/A')

// 生成测试数据
const generateTestData = () => {
  console.log('🧪 生成测试数据...')
  
  const items: TestItem[] = []
  const count = 259 // 使用报错时的数量
  
  for (let i = 0; i < count; i++) {
    items.push({
      id: i,
      name: `测试项目 ${i}`,
      type: i % 3 === 0 ? 'upload' : i % 3 === 1 ? 'download' : 'batch'
    })
  }
  
  testItems.value = items
  console.log(`✅ 生成了 ${count} 个测试项目`)
  updatePerformanceInfo()
}

// 清除测试数据
const clearTestData = () => {
  testItems.value = []
  console.log('🧹 测试数据已清除')
  updatePerformanceInfo()
}

// 清除错误日志
const clearErrors = () => {
  errorLogs.value = []
}

// 更新性能信息
const updatePerformanceInfo = () => {
  domNodeCount.value = document.querySelectorAll('*').length
  
  // @ts-ignore
  const memory = (performance as any).memory
  if (memory) {
    const used = Math.round(memory.usedJSHeapSize / 1048576)
    const total = Math.round(memory.totalJSHeapSize / 1048576)
    memoryUsage.value = `${used}MB / ${total}MB`
  }
}

// 捕获全局错误
const handleError = (event: ErrorEvent) => {
  const errorMsg = `${event.error?.name || 'Error'}: ${event.error?.message || event.message}`
  errorLogs.value.push(errorMsg)
  console.error('捕获到错误:', event.error)
}

// 捕获未处理的 Promise 拒绝
const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
  const errorMsg = `Promise Rejection: ${event.reason}`
  errorLogs.value.push(errorMsg)
  console.error('捕获到 Promise 拒绝:', event.reason)
}

// 定期更新性能信息
let performanceInterval: NodeJS.Timeout

onMounted(() => {
  // 添加错误监听器
  window.addEventListener('error', handleError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
  
  // 定期更新性能信息
  performanceInterval = setInterval(updatePerformanceInfo, 2000)
  updatePerformanceInfo()
  
  console.log('🔧 虚拟列表测试页面已加载')
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('error', handleError)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
  
  // 清理定时器
  if (performanceInterval) {
    clearInterval(performanceInterval)
  }
})
</script>
