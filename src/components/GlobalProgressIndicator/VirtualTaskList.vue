<template>
  <div ref="containerRef" class="virtual-task-list" :style="{ height: `${height}px`, overflow: 'auto' }">
    <div :style="{ height: `${totalSize}px`, position: 'relative' }">
      <div v-for="virtualItem in virtualItems" :key="virtualItem.key" :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: `${virtualItem.size}px`,
        transform: `translateY(${virtualItem.start}px)`
      }">
        <slot :item="items[virtualItem.index]" :index="virtualItem.index" :virtualItem="virtualItem" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'

interface Props {
  items: T[]
  height: number
  estimateSize?: number
  overscan?: number
  getItemKey?: (item: T, index: number) => string | number
}

const props = withDefaults(defineProps<Props>(), {
  estimateSize: 60,
  overscan: 5,
  getItemKey: (item: T, index: number) => index
})

const containerRef = ref<HTMLElement>()

// 使用简单的虚拟滚动实现，避免 @tanstack/vue-virtual 的API问题
const scrollTop = ref(0)

// 计算总高度
const totalSize = computed(() => props.items.length * props.estimateSize)

// 计算可见区域
const visibleRange = computed(() => {
  const containerHeight = props.height
  const startIndex = Math.floor(scrollTop.value / props.estimateSize)
  const endIndex = Math.min(
    props.items.length - 1,
    Math.ceil((scrollTop.value + containerHeight) / props.estimateSize)
  )

  // 添加 overscan
  const overscanStart = Math.max(0, startIndex - props.overscan)
  const overscanEnd = Math.min(props.items.length - 1, endIndex + props.overscan)

  return {
    start: overscanStart,
    end: overscanEnd
  }
})

// 计算可见项目
const virtualItems = computed(() => {
  const { start, end } = visibleRange.value
  const items = []

  for (let i = start; i <= end; i++) {
    if (i < props.items.length) {
      items.push({
        key: props.getItemKey(props.items[i], i),
        index: i,
        start: i * props.estimateSize,
        size: props.estimateSize
      })
    }
  }

  return items
})

// 处理滚动事件
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
}

// 监听 items 变化
watch(() => props.items.length, (newLength, oldLength) => {
  console.log(`虚拟列表项目数量变化: ${oldLength} -> ${newLength}`)
}, { flush: 'post' })

// 提供滚动到指定项的方法
const scrollToItem = (index: number, options?: { align?: 'start' | 'center' | 'end' | 'auto' }) => {
  if (!containerRef.value) return

  const itemOffset = index * props.estimateSize
  let scrollOffset = itemOffset

  if (options?.align === 'center') {
    scrollOffset = itemOffset - (props.height - props.estimateSize) / 2
  } else if (options?.align === 'end') {
    scrollOffset = itemOffset - props.height + props.estimateSize
  }

  scrollOffset = Math.max(0, Math.min(scrollOffset, totalSize.value - props.height))
  containerRef.value.scrollTop = scrollOffset
}

// 提供滚动到顶部的方法
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 提供滚动到底部的方法
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = totalSize.value
  }
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom
})

// 添加调试信息
onMounted(() => {
  console.log('VirtualTaskList mounted with', props.items.length, 'items')

  // 添加滚动事件监听器
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
  }
})

// 性能优化：在组件卸载时清理
onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
.virtual-task-list {
  /* 确保滚动条样式一致 */
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.virtual-task-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-task-list::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-task-list::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.virtual-task-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}
</style>
