<template>
  <div ref="containerRef" class="virtual-task-list" :style="{ height: `${height}px`, overflow: 'auto' }">
    <div :style="{ height: `${virtualizer.getTotalSize()}px`, position: 'relative' }">
      <div
        v-for="virtualItem in virtualizer.getVirtualItems()"
        :key="virtualItem.key"
        :style="{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: `${virtualItem.size}px`,
          transform: `translateY(${virtualItem.start}px)`
        }"
      >
        <slot
          :item="items[virtualItem.index]"
          :index="virtualItem.index"
          :virtualItem="virtualItem"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'

interface Props {
  items: T[]
  height: number
  estimateSize?: number
  overscan?: number
  getItemKey?: (item: T, index: number) => string | number
}

const props = withDefaults(defineProps<Props>(), {
  estimateSize: 60,
  overscan: 5,
  getItemKey: (item: T, index: number) => index
})

const containerRef = ref<HTMLElement>()

// 创建虚拟化器
const virtualizer = useVirtualizer(
  computed(() => ({
    count: props.items.length,
    getScrollElement: () => containerRef.value,
    estimateSize: () => props.estimateSize,
    overscan: props.overscan,
    getItemKey: (index: number) => props.getItemKey(props.items[index], index)
  }))
)

// 监听 items 变化，重新计算虚拟化
watch(() => props.items.length, () => {
  virtualizer.measure()
}, { flush: 'post' })

// 提供滚动到指定项的方法
const scrollToItem = (index: number, options?: { align?: 'start' | 'center' | 'end' | 'auto' }) => {
  virtualizer.scrollToIndex(index, options)
}

// 提供滚动到顶部的方法
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 提供滚动到底部的方法
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = containerRef.value.scrollHeight
  }
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  virtualizer
})

// 性能优化：在组件卸载时清理
onUnmounted(() => {
  // 清理可能的定时器或监听器
})
</script>

<style scoped>
.virtual-task-list {
  /* 确保滚动条样式一致 */
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.virtual-task-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-task-list::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-task-list::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.virtual-task-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}
</style>
